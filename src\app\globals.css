@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 40 20% 97%;
    --foreground: 240 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 240 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 20% 15%;

    --primary: 240 35% 25%;
    --primary-foreground: 0 0% 98%;

    --secondary: 40 20% 97%;
    --secondary-foreground: 240 20% 15%;

    --muted: 40 15% 92%;
    --muted-foreground: 240 10% 45%;

    --accent: 195 65% 60%;
    --accent-foreground: 0 0% 98%;

    --coral: 15 85% 60%;
    --coral-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 40 20% 90%;
    --input: 40 20% 90%;
    --ring: 240 35% 25%;

    --header-bg: 240 35% 25%;
    --section-bg: 40 20% 97%;
    --hero-bg: linear-gradient(135deg, hsl(40 20% 97%), hsl(40 25% 95%));
    --why-choose-bg: hsl(240 35% 25%);
    --services-bg: hsl(40 20% 97%);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 35% 25%;
    --foreground: 0 0% 98%;

    --card: 240 30% 30%;
    --card-foreground: 0 0% 98%;

    --popover: 240 30% 30%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 35% 25%;

    --secondary: 240 30% 30%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 25% 35%;
    --muted-foreground: 240 10% 70%;

    --accent: 195 65% 60%;
    --accent-foreground: 240 35% 25%;

    --coral: 15 85% 60%;
    --coral-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 25% 35%;
    --input: 240 25% 35%;
    --ring: 195 65% 60%;

    --header-bg: 240 35% 25%;
    --section-bg: 240 30% 30%;
    --hero-bg: linear-gradient(135deg, hsl(240 30% 30%), hsl(240 25% 28%));
    --why-choose-bg: hsl(240 35% 25%);
    --services-bg: hsl(240 30% 30%);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
