import { Instagram, Linkedin, Twitter, Facebook, Mail, Phone, MapPin } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="text-2xl font-bold mb-4">
              <span className="text-accent">Skyline</span> Digital Solution
            </div>
            <p className="text-primary-foreground/80 mb-6 max-w-md">
              We&apos;re not your standard agency. Skyline Digital Solution builds brands that dominate
              with sharp strategy and wild creativity.
            </p>
            <div className="flex space-x-4">
              <Instagram className="h-6 w-6 text-coral hover:text-coral/80 cursor-pointer transition-colors" />
              <Linkedin className="h-6 w-6 text-accent hover:text-accent/80 cursor-pointer transition-colors" />
              <Twitter className="h-6 w-6 text-primary-foreground hover:text-accent cursor-pointer transition-colors" />
              <Facebook className="h-6 w-6 text-accent hover:text-accent/80 cursor-pointer transition-colors" />
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#home" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="#services" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Services
                </a>
              </li>
              <li>
                <a href="#about" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  About
                </a>
              </li>
              <li>
                <a href="#contact" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="h-5 w-5 text-accent mr-3" />
                <span className="text-primary-foreground/80"><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="h-5 w-5 text-accent mr-3" />
                <span className="text-primary-foreground/80">+****************</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-accent mr-3" />
                <span className="text-primary-foreground/80">New York, NY</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center">
          <p className="text-primary-foreground/60">
            © 2024 Skyline Digital Solution. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
