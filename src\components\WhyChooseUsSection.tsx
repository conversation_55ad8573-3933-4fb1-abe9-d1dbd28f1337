import { Clock, TrendingUp, Target } from "lucide-react";

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: Clock,
      title: "Save Time, Scale Faster",
      description: "Efficient workflows and fast turnarounds help you launch projects quickly and stay ahead of the competition."
    },
    {
      icon: TrendingUp,
      title: "Invest Smart, Get More",
      description: "High-value services tailored to your goals, giving you premium results that fit your budget."
    },
    {
      icon: Target,
      title: "Strategic Excellence",
      description: "Your strategic partner for visionary solutions, proven expertise, and measurable impact."
    }
  ];

  return (
    <section className="bg-why-choose-bg py-16 lg:py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-primary-foreground mb-4">
            Why Choose Us
          </h2>
          <p className="text-xl text-primary-foreground/80 max-w-3xl mx-auto">
            Your strategic partner for visionary solutions, proven expertise, and measurable impact.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-coral rounded-full mb-6">
                <feature.icon className="h-8 w-8 text-coral-foreground" />
              </div>
              <h3 className="text-xl font-bold text-primary-foreground mb-4">
                {feature.title}
              </h3>
              <p className="text-primary-foreground/80 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;