import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Star, User, Globe, ShoppingCart, Share2, BarChart3 } from "lucide-react";

const ServicesSection = () => {
  const services = [
    {
      icon: User,
      title: "Career Branding",
      description: "From entry-level to executive, Skyline Digital Solution builds personal brands that open doors. We position you for success.",
      features: [
        "Executive Resume/CV Development",
        "Tailored Cover Letter", 
        "ATS Resume",
        "LinkedIn Profile Optimization"
      ]
    },
    {
      icon: Globe,
      title: "WordPress Development", 
      description: "We build sleek, lightning-fast WordPress sites templates. No fluff. Just performance.",
      features: [
        "Custom Design & Development",
        "Landing Pages",
        "Website Redesign",
        "Maintenance"
      ]
    },
    {
      icon: ShoppingCart,
      title: "Shopify Development",
      description: "Skyline Digital Solution designs custom Shopify stores that make people click, buy, and come back. Seamless UX, bold visuals, and revenue-driven design.",
      features: [
        "Custom Store Setup",
        "Product Page Design",
        "Payment Integration", 
        "Store Optimization"
      ]
    },
    {
      icon: Share2,
      title: "Social Media Handling",
      description: "We combine creative energy with data-driven strategy to get your brand seen, clicked, and converting. No fluff. Just results.",
      features: [
        "Content Creation",
        "Page Management",
        "Growth Campaigns"
      ]
    },
    {
      icon: BarChart3,
      title: "Digital Marketing",
      description: "We combine creative energy with data-driven strategy to get your brand seen, clicked, and converting. No fluff. Just results.",
      features: [
        "SEO & SEM",
        "Paid Ads", 
        "Email Marketing"
      ]
    },
    {
      icon: ShoppingCart,
      title: "Shopify Development",
      description: "Skyline Digital Solution designs custom Shopify stores that make people click, buy, and come back. Seamless UX, bold visuals, and revenue-driven design.",
      features: [
        "Custom Store Setup",
        "Product Page Design",
        "Payment Integration"
      ]
    }
  ];

  return (
    <section className="bg-services-bg py-16 lg:py-20">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-primary mb-4">
            Our Services & <span className="text-coral">Features</span>
          </h2>
          <p className="text-xl text-accent font-medium mb-6">
            Designed to Impress. Built to Perform.
          </p>
          <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Skyline Digital Solution transforms ideas into digital powerhouses — from personal branding and design to full-scale 
            web development, e-commerce, and marketing strategies built to make your brand impossible to ignore.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="bg-card hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
              <CardHeader className="text-center pb-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-lg mb-4 mx-auto">
                  <Star className="h-8 w-8 text-accent-foreground" />
                </div>
                <CardTitle className="text-xl font-bold text-card-foreground mb-2">
                  {service.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground leading-relaxed">
                  {service.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-accent rounded-full mr-3 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;