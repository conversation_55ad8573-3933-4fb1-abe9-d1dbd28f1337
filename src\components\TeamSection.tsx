import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const TeamSection = () => {
  const teamMembers = [
    {
      name: "<PERSON><PERSON> <PERSON><PERSON>",
      role: "Shopify Expert",
      description: "E-commerce architect creating conversion-focused Shopify stores that captivate and convert.",
      avatar: "/placeholder.svg"
    },
    {
      name: "M. Esa", 
      role: "Shopify Expert",
      description: "E-commerce architect creating conversion-focused Shopify stores that captivate and convert.",
      avatar: "/placeholder.svg"
    },
    {
      name: "<PERSON><PERSON> E<PERSON>",
      role: "Shopify Expert", 
      description: "E-commerce architect creating conversion-focused Shopify stores that captivate and convert.",
      avatar: "/placeholder.svg"
    }
  ];

  return (
    <section className="bg-services-bg py-16 lg:py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-primary mb-4">
            Leadership Team
          </h2>
          <p className="text-xl text-muted-foreground">
            The creative minds turning your ideas into extraordinary results.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="bg-card text-center hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
              <CardHeader className="pb-4">
                <div className="flex justify-center mb-4">
                  <Avatar className="w-24 h-24">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback className="bg-accent text-2xl font-bold">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <CardTitle className="text-2xl font-bold text-card-foreground mb-2">
                  {member.name}
                </CardTitle>
                <CardDescription className="text-coral font-semibold text-lg">
                  {member.role}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {member.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamSection;