import { <PERSON><PERSON> } from "@/components/ui/button";
import { Instagram, Linkedin, Twitter, Facebook } from "lucide-react";

const Header = () => {
  return (
    <header className="bg-header-bg text-primary-foreground">
      {/* Top banner */}
      <div className="bg-primary/90 py-2 text-center text-sm">
        <span className="text-primary-foreground">Get 10% off all services this month.</span>
      </div>
      
      {/* Main header */}
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <div className="text-2xl font-bold text-primary-foreground">
              <span className="text-accent">Skyline</span> Digital Solution
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-primary-foreground hover:text-accent transition-colors">
              HOME
            </a>
            <a href="#services" className="text-primary-foreground hover:text-accent transition-colors">
              SERVICES
            </a>
            <a href="#about" className="text-primary-foreground hover:text-accent transition-colors">
              ABOUT
            </a>
            <a href="#contact" className="text-primary-foreground hover:text-accent transition-colors">
              CONTACT
            </a>
          </nav>

          {/* Right side - Social icons and CTA */}
          <div className="flex items-center space-x-4">
            {/* Social icons */}
            <div className="hidden md:flex items-center space-x-3">
              <Instagram className="h-5 w-5 text-coral hover:text-coral/80 cursor-pointer transition-colors" />
              <Linkedin className="h-5 w-5 text-accent hover:text-accent/80 cursor-pointer transition-colors" />
              <Twitter className="h-5 w-5 text-primary-foreground hover:text-accent cursor-pointer transition-colors" />
              <Facebook className="h-5 w-5 text-accent hover:text-accent/80 cursor-pointer transition-colors" />
            </div>
            
            {/* CTA Button */}
            <Button variant="coral" size="sm" className="rounded-full px-6">
              Book free 15-min chat
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;