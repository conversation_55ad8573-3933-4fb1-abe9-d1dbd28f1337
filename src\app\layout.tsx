import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Skyline Digital Solution - Expert Digital Marketing Services",
  description: "We're not your standard agency. Skyline Digital Solution builds brands that dominate with sharp strategy and wild creativity. Expert digital marketing services for bold brands and creators.",
  keywords: ["digital marketing", "brand strategy", "creative agency", "business growth", "marketing services"],
  authors: [{ name: "Skyline Digital Solution" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "Skyline Digital Solution - Expert Digital Marketing Services",
    description: "We're not your standard agency. Skyline Digital Solution builds brands that dominate with sharp strategy and wild creativity.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Skyline Digital Solution - Expert Digital Marketing Services",
    description: "We're not your standard agency. Skyline Digital Solution builds brands that dominate with sharp strategy and wild creativity.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
       <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
