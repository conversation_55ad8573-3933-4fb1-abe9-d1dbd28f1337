import { Button } from "@/components/ui/button";

const AchievementsSection = () => {
  const stats = [
    { number: "340+", label: "Projects Delivered" },
    { number: "325+", label: "Satisfied Clients" },
    { number: "95+", label: "Client Retention" },
    { number: "7+", label: "Team Members" }
  ];

  return (
    <section className="bg-section-bg py-16 lg:py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-primary mb-4">
            Achievements
          </h2>
          <p className="text-2xl text-primary font-semibold mb-8">
            Milestones That Define Our Impact
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-coral mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-medium text-primary">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Description */}
        <div className="text-center max-w-4xl mx-auto mb-8">
          <p className="text-lg text-muted-foreground leading-relaxed">
            At Skyline Digital Solution, every milestone marks a story of innovation, growth, and client success. 
            From launching standout brands to driving measurable digital results, our achievements reflect our 
            commitment to transforming businesses worldwide.
          </p>
        </div>

        <div className="text-center">
          <Button variant="coral" size="lg" className="px-8 py-6 text-lg font-semibold">
            READ MORE
          </Button>
        </div>
      </div>
    </section>
  );
};

export default AchievementsSection;